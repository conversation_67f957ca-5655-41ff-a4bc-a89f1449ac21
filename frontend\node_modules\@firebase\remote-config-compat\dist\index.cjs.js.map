{"version": 3, "file": "index.cjs.js", "sources": ["../src/remoteConfig.ts", "../src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, _FirebaseService } from '@firebase/app-compat';\nimport {\n  Value as ValueCompat,\n  FetchStatus as FetchSTatusCompat,\n  Settings as SettingsCompat,\n  LogLevel as RemoteConfigLogLevel,\n  RemoteConfig as RemoteConfigCompat\n} from '@firebase/remote-config-types';\nimport {\n  RemoteConfig,\n  setLogLevel,\n  activate,\n  ensureInitialized,\n  fetchAndActivate,\n  fetchConfig,\n  getAll,\n  getBoolean,\n  getNumber,\n  getString,\n  getValue,\n  isSupported\n} from '@firebase/remote-config';\n\nexport { isSupported };\n\nexport class RemoteConfigCompatImpl\n  implements RemoteConfigCompat, _FirebaseService\n{\n  constructor(public app: FirebaseApp, readonly _delegate: RemoteConfig) {}\n\n  get defaultConfig(): { [key: string]: string | number | boolean } {\n    return this._delegate.defaultConfig;\n  }\n\n  set defaultConfig(value: { [key: string]: string | number | boolean }) {\n    this._delegate.defaultConfig = value;\n  }\n\n  get fetchTimeMillis(): number {\n    return this._delegate.fetchTimeMillis;\n  }\n\n  get lastFetchStatus(): FetchSTatusCompat {\n    return this._delegate.lastFetchStatus;\n  }\n\n  get settings(): SettingsCompat {\n    return this._delegate.settings;\n  }\n\n  set settings(value: SettingsCompat) {\n    this._delegate.settings = value;\n  }\n\n  activate(): Promise<boolean> {\n    return activate(this._delegate);\n  }\n\n  ensureInitialized(): Promise<void> {\n    return ensureInitialized(this._delegate);\n  }\n\n  /**\n   * @throws a {@link ErrorCode.FETCH_CLIENT_TIMEOUT} if the request takes longer than\n   * {@link Settings.fetchTimeoutInSeconds} or\n   * {@link DEFAULT_FETCH_TIMEOUT_SECONDS}.\n   */\n  fetch(): Promise<void> {\n    return fetchConfig(this._delegate);\n  }\n\n  fetchAndActivate(): Promise<boolean> {\n    return fetchAndActivate(this._delegate);\n  }\n\n  getAll(): { [key: string]: ValueCompat } {\n    return getAll(this._delegate);\n  }\n\n  getBoolean(key: string): boolean {\n    return getBoolean(this._delegate, key);\n  }\n\n  getNumber(key: string): number {\n    return getNumber(this._delegate, key);\n  }\n\n  getString(key: string): string {\n    return getString(this._delegate, key);\n  }\n\n  getValue(key: string): ValueCompat {\n    return getValue(this._delegate, key);\n  }\n\n  // Based on packages/firestore/src/util/log.ts but not static because we need per-instance levels\n  // to differentiate 2p and 3p use-cases.\n  setLogLevel(logLevel: RemoteConfigLogLevel): void {\n    setLogLevel(this._delegate, logLevel);\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport firebase, { _FirebaseNamespace } from '@firebase/app-compat';\nimport {\n  Component,\n  ComponentContainer,\n  ComponentType,\n  InstanceFactoryOptions\n} from '@firebase/component';\nimport { RemoteConfigCompatImpl, isSupported } from './remoteConfig';\nimport { name as packageName, version } from '../package.json';\nimport { RemoteConfig as RemoteConfigCompat } from '@firebase/remote-config-types';\n\nfunction registerRemoteConfigCompat(\n  firebaseInstance: _FirebaseNamespace\n): void {\n  firebaseInstance.INTERNAL.registerComponent(\n    new Component(\n      'remoteConfig-compat',\n      remoteConfigFactory,\n      ComponentType.PUBLIC\n    )\n      .setMultipleInstances(true)\n      .setServiceProps({ isSupported })\n  );\n\n  firebaseInstance.registerVersion(packageName, version);\n}\n\nfunction remoteConfigFactory(\n  container: ComponentContainer,\n  { instanceIdentifier: namespace }: InstanceFactoryOptions\n): RemoteConfigCompatImpl {\n  const app = container.getProvider('app-compat').getImmediate();\n  // The following call will always succeed because rc `import {...} from '@firebase/remote-config'`\n  const remoteConfig = container.getProvider('remote-config').getImmediate({\n    identifier: namespace\n  });\n\n  return new RemoteConfigCompatImpl(app, remoteConfig);\n}\n\nregisterRemoteConfigCompat(firebase as _FirebaseNamespace);\n\ndeclare module '@firebase/app-compat' {\n  interface FirebaseNamespace {\n    remoteConfig: {\n      (app?: FirebaseApp): RemoteConfigCompat;\n    };\n  }\n  interface FirebaseApp {\n    remoteConfig(): RemoteConfigCompat;\n  }\n}\n"], "names": ["activate", "ensureInitialized", "fetchConfig", "fetchAndActivate", "getAll", "getBoolean", "getNumber", "getString", "getValue", "setLogLevel", "Component", "packageName", "firebase"], "mappings": ";;;;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AA2BH,IAAA,sBAAA,kBAAA,YAAA;IAGE,SAAmB,sBAAA,CAAA,GAAgB,EAAW,SAAuB,EAAA;QAAlD,IAAG,CAAA,GAAA,GAAH,GAAG,CAAa;QAAW,IAAS,CAAA,SAAA,GAAT,SAAS,CAAc;KAAI;AAEzE,IAAA,MAAA,CAAA,cAAA,CAAI,sBAAa,CAAA,SAAA,EAAA,eAAA,EAAA;AAAjB,QAAA,GAAA,EAAA,YAAA;AACE,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;SACrC;AAED,QAAA,GAAA,EAAA,UAAkB,KAAmD,EAAA;AACnE,YAAA,IAAI,CAAC,SAAS,CAAC,aAAa,GAAG,KAAK,CAAC;SACtC;;;AAJA,KAAA,CAAA,CAAA;AAMD,IAAA,MAAA,CAAA,cAAA,CAAI,sBAAe,CAAA,SAAA,EAAA,iBAAA,EAAA;AAAnB,QAAA,GAAA,EAAA,YAAA;AACE,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;SACvC;;;AAAA,KAAA,CAAA,CAAA;AAED,IAAA,MAAA,CAAA,cAAA,CAAI,sBAAe,CAAA,SAAA,EAAA,iBAAA,EAAA;AAAnB,QAAA,GAAA,EAAA,YAAA;AACE,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;SACvC;;;AAAA,KAAA,CAAA,CAAA;AAED,IAAA,MAAA,CAAA,cAAA,CAAI,sBAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;AAAZ,QAAA,GAAA,EAAA,YAAA;AACE,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;SAChC;AAED,QAAA,GAAA,EAAA,UAAa,KAAqB,EAAA;AAChC,YAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,KAAK,CAAC;SACjC;;;AAJA,KAAA,CAAA,CAAA;AAMD,IAAA,sBAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;AACE,QAAA,OAAOA,qBAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KACjC,CAAA;AAED,IAAA,sBAAA,CAAA,SAAA,CAAA,iBAAiB,GAAjB,YAAA;AACE,QAAA,OAAOC,8BAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KAC1C,CAAA;AAED;;;;AAIG;AACH,IAAA,sBAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;AACE,QAAA,OAAOC,wBAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KACpC,CAAA;AAED,IAAA,sBAAA,CAAA,SAAA,CAAA,gBAAgB,GAAhB,YAAA;AACE,QAAA,OAAOC,6BAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KACzC,CAAA;AAED,IAAA,sBAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;AACE,QAAA,OAAOC,mBAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KAC/B,CAAA;IAED,sBAAU,CAAA,SAAA,CAAA,UAAA,GAAV,UAAW,GAAW,EAAA;QACpB,OAAOC,uBAAU,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;KACxC,CAAA;IAED,sBAAS,CAAA,SAAA,CAAA,SAAA,GAAT,UAAU,GAAW,EAAA;QACnB,OAAOC,sBAAS,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;KACvC,CAAA;IAED,sBAAS,CAAA,SAAA,CAAA,SAAA,GAAT,UAAU,GAAW,EAAA;QACnB,OAAOC,sBAAS,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;KACvC,CAAA;IAED,sBAAQ,CAAA,SAAA,CAAA,QAAA,GAAR,UAAS,GAAW,EAAA;QAClB,OAAOC,qBAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;KACtC,CAAA;;;IAID,sBAAW,CAAA,SAAA,CAAA,WAAA,GAAX,UAAY,QAA8B,EAAA;AACxC,QAAAC,wBAAW,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;KACvC,CAAA;IACH,OAAC,sBAAA,CAAA;AAAD,CAAC,EAAA,CAAA;;;;;ACrHD;;;;;;;;;;;;;;;AAeG;AAaH,SAAS,0BAA0B,CACjC,gBAAoC,EAAA;IAEpC,gBAAgB,CAAC,QAAQ,CAAC,iBAAiB,CACzC,IAAIC,mBAAS,CACX,qBAAqB,EACrB,mBAAmB,EAEpB,QAAA,4BAAA;SACE,oBAAoB,CAAC,IAAI,CAAC;AAC1B,SAAA,eAAe,CAAC,EAAE,WAAW,0BAAA,EAAE,CAAC,CACpC,CAAC;AAEF,IAAA,gBAAgB,CAAC,eAAe,CAACC,IAAW,EAAE,OAAO,CAAC,CAAC;AACzD,CAAC;AAED,SAAS,mBAAmB,CAC1B,SAA6B,EAC7B,EAAyD,EAAA;AAAnC,IAAA,IAAA,SAAS,GAAA,EAAA,CAAA,kBAAA,CAAA;IAE/B,IAAM,GAAG,GAAG,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,YAAY,EAAE,CAAC;;IAE/D,IAAM,YAAY,GAAG,SAAS,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC;AACvE,QAAA,UAAU,EAAE,SAAS;AACtB,KAAA,CAAC,CAAC;AAEH,IAAA,OAAO,IAAI,sBAAsB,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;AACvD,CAAC;AAED,0BAA0B,CAACC,4BAA8B,CAAC;;"}
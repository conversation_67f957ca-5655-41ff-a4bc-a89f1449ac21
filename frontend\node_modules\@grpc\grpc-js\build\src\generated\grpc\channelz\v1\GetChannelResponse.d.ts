import type { Channel as _grpc_channelz_v1_Channel, Channel__Output as _grpc_channelz_v1_Channel__Output } from '../../../grpc/channelz/v1/Channel';
export interface GetChannelResponse {
    /**
     * The Channel that corresponds to the requested channel_id.  This field
     * should be set.
     */
    'channel'?: (_grpc_channelz_v1_Channel | null);
}
export interface GetChannelResponse__Output {
    /**
     * The Channel that corresponds to the requested channel_id.  This field
     * should be set.
     */
    'channel': (_grpc_channelz_v1_Channel__Output | null);
}

{"name": "@firebase/installations-compat", "version": "0.2.9", "author": "Firebase <<EMAIL>> (https://firebase.google.com/)", "main": "dist/index.cjs.js", "module": "dist/esm/index.esm2017.js", "browser": "dist/esm/index.esm2017.js", "esm5": "dist/esm/index.esm.js", "exports": {".": {"types": "./dist/src/index.d.ts", "require": "./dist/index.cjs.js", "esm5": "./dist/esm/index.esm.js", "default": "./dist/esm/index.esm2017.js"}, "./package.json": "./package.json"}, "typings": "dist/src/index.d.ts", "license": "Apache-2.0", "files": ["dist"], "scripts": {"lint": "eslint -c .eslintrc.js '**/*.ts' --ignore-path '../../.gitignore'", "lint:fix": "eslint --fix -c .eslintrc.js '**/*.ts' --ignore-path '../../.gitignore'", "build": "rollup -c", "build:deps": "lerna run --scope @firebase/installations-compat --include-dependencies build", "build:release": "rollup -c rollup.config.release.js", "dev": "rollup -c -w", "test": "yarn type-check && yarn test:karma && yarn lint", "test:ci": "node ../../scripts/run_tests_in_ci.js", "test:karma": "karma start", "test:debug": "karma start --browsers=Chrome --auto-watch", "trusted-type-check": "tsec -p tsconfig.json --noEmit", "type-check": "tsc -p . --noEmit", "serve": "yarn serve:build && yarn serve:host", "serve:build": "rollup -c test-app/rollup.config.js", "serve:host": "http-server -c-1 test-app"}, "repository": {"directory": "packages/installations-compat", "type": "git", "url": "git+https://github.com/firebase/firebase-js-sdk.git"}, "bugs": {"url": "https://github.com/firebase/firebase-js-sdk/issues"}, "devDependencies": {"@firebase/app-compat": "0.2.41", "rollup": "2.79.1", "@rollup/plugin-commonjs": "21.1.0", "@rollup/plugin-json": "4.1.0", "@rollup/plugin-node-resolve": "13.3.0", "rollup-plugin-typescript2": "0.31.2", "rollup-plugin-uglify": "6.0.4", "typescript": "4.7.4"}, "peerDependencies": {"@firebase/app-compat": "0.x"}, "dependencies": {"@firebase/installations": "0.6.9", "@firebase/installations-types": "0.5.2", "@firebase/util": "1.10.0", "@firebase/component": "0.6.9", "tslib": "^2.1.0"}}
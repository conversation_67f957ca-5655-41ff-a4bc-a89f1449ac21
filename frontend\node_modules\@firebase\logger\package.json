{"name": "@firebase/logger", "version": "0.4.2", "description": "A logger package for use in the Firebase JS SDK", "author": "Firebase <<EMAIL>> (https://firebase.google.com/)", "main": "dist/index.cjs.js", "module": "dist/esm/index.esm2017.js", "esm5": "dist/esm/index.esm5.js", "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.cjs.js", "esm5": "./dist/esm/index.esm5.js", "default": "./dist/esm/index.esm2017.js"}, "./package.json": "./package.json"}, "files": ["dist"], "scripts": {"lint": "eslint -c .eslintrc.js '**/*.ts' --ignore-path '../../.gitignore'", "lint:fix": "eslint --fix -c .eslintrc.js '**/*.ts' --ignore-path '../../.gitignore'", "build": "rollup -c", "build:deps": "lerna run --scope @firebase/logger --include-dependencies build", "dev": "rollup -c -w", "test": "run-p --npm-path npm lint test:all", "test:ci": "node ../../scripts/run_tests_in_ci.js -s test:all", "test:all": "run-p --npm-path npm test:browser test:node", "test:browser": "karma start --single-run", "test:browser:debug": "karma start --browsers Chrome --auto-watch", "test:node": "TS_NODE_COMPILER_OPTIONS='{\"module\":\"commonjs\"}' nyc --reporter lcovonly -- mocha test/**/*.test.* --config ../../config/mocharc.node.js"}, "license": "Apache-2.0", "dependencies": {"tslib": "^2.1.0"}, "devDependencies": {"rollup": "2.79.1", "rollup-plugin-typescript2": "0.31.2", "typescript": "4.7.4"}, "repository": {"directory": "packages/logger", "type": "git", "url": "git+https://github.com/firebase/firebase-js-sdk.git"}, "bugs": {"url": "https://github.com/firebase/firebase-js-sdk/issues"}, "typings": "dist/index.d.ts", "nyc": {"extension": [".ts"], "reportDir": "./coverage/node"}}
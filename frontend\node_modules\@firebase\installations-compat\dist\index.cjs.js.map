{"version": 3, "file": "index.cjs.js", "sources": ["../src/installationsCompat.ts", "../src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseInstallations as FirebaseInstallationsCompat } from '@firebase/installations-types';\nimport { FirebaseApp, _FirebaseService } from '@firebase/app-compat';\nimport {\n  Installations,\n  deleteInstallations,\n  getId,\n  getToken,\n  IdChangeCallbackFn,\n  IdChangeUnsubscribeFn,\n  onIdChange\n} from '@firebase/installations';\n\nexport class InstallationsCompat\n  implements FirebaseInstallationsCompat, _FirebaseService\n{\n  constructor(public app: FirebaseApp, readonly _delegate: Installations) {}\n\n  getId(): Promise<string> {\n    return getId(this._delegate);\n  }\n  getToken(forceRefresh?: boolean): Promise<string> {\n    return getToken(this._delegate, forceRefresh);\n  }\n  delete(): Promise<void> {\n    return deleteInstallations(this._delegate);\n  }\n  onIdChange(callback: IdChangeCallbackFn): IdChangeUnsubscribeFn {\n    return onIdChange(this._delegate, callback);\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport firebase, { _FirebaseNamespace } from '@firebase/app-compat';\nimport { name, version } from '../package.json';\nimport { Component, ComponentType } from '@firebase/component';\nimport { FirebaseInstallations as FirebaseInstallationsCompat } from '@firebase/installations-types';\nimport { InstallationsCompat } from './installationsCompat';\n\ndeclare module '@firebase/component' {\n  interface NameServiceMapping {\n    'installations-compat': FirebaseInstallationsCompat;\n  }\n}\n\nfunction registerInstallations(instance: _FirebaseNamespace): void {\n  instance.INTERNAL.registerComponent(\n    new Component(\n      'installations-compat',\n      container => {\n        const app = container.getProvider('app-compat').getImmediate()!;\n        const installations = container\n          .getProvider('installations')\n          .getImmediate()!;\n        return new InstallationsCompat(app, installations);\n      },\n      ComponentType.PUBLIC\n    )\n  );\n\n  instance.registerVersion(name, version);\n}\n\nregisterInstallations(firebase as _FirebaseNamespace);\n\n/**\n * Define extension behavior of `registerInstallations`\n */\ndeclare module '@firebase/app-compat' {\n  interface FirebaseNamespace {\n    installations(app?: FirebaseApp): FirebaseInstallationsCompat;\n  }\n  interface FirebaseApp {\n    installations(): FirebaseInstallationsCompat;\n  }\n}\n"], "names": ["getId", "getToken", "deleteInstallations", "onIdChange", "Component", "firebase"], "mappings": ";;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AAcH,IAAA,mBAAA,kBAAA,YAAA;IAGE,SAAmB,mBAAA,CAAA,GAAgB,EAAW,SAAwB,EAAA;QAAnD,IAAG,CAAA,GAAA,GAAH,GAAG,CAAa;QAAW,IAAS,CAAA,SAAA,GAAT,SAAS,CAAe;KAAI;AAE1E,IAAA,mBAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;AACE,QAAA,OAAOA,mBAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KAC9B,CAAA;IACD,mBAAQ,CAAA,SAAA,CAAA,QAAA,GAAR,UAAS,YAAsB,EAAA;QAC7B,OAAOC,sBAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;KAC/C,CAAA;AACD,IAAA,mBAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;AACE,QAAA,OAAOC,iCAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KAC5C,CAAA;IACD,mBAAU,CAAA,SAAA,CAAA,UAAA,GAAV,UAAW,QAA4B,EAAA;QACrC,OAAOC,wBAAU,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;KAC7C,CAAA;IACH,OAAC,mBAAA,CAAA;AAAD,CAAC,EAAA,CAAA;;AC9CD;;;;;;;;;;;;;;;AAeG;AAcH,SAAS,qBAAqB,CAAC,QAA4B,EAAA;IACzD,QAAQ,CAAC,QAAQ,CAAC,iBAAiB,CACjC,IAAIC,mBAAS,CACX,sBAAsB,EACtB,UAAA,SAAS,EAAA;QACP,IAAM,GAAG,GAAG,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,YAAY,EAAG,CAAC;QAChE,IAAM,aAAa,GAAG,SAAS;aAC5B,WAAW,CAAC,eAAe,CAAC;AAC5B,aAAA,YAAY,EAAG,CAAC;AACnB,QAAA,OAAO,IAAI,mBAAmB,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;KACpD,EAAA,QAAA,4BAEF,CACF,CAAC;AAEF,IAAA,QAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC1C,CAAC;AAED,qBAAqB,CAACC,4BAA8B,CAAC;;"}